import kotlin.random.Random

class NumberGuessingGame {
    private val targetNumber = Random.nextInt(1, 101)
    private var attempts = 0
    private val maxAttempts = 7
    
    fun startGame() {
        println("🎮 Chào mừng đến với trò chơi Đoán <PERSON>ố!")
        println("Tôi đã nghĩ ra một số từ 1 đến 100.")
        println("Bạn có $maxAttempts lần đoán. Chúc may mắn!")
        println("=" * 40)
        
        while (attempts < maxAttempts) {
            print("Lần đoán ${attempts + 1}/$maxAttempts - Nhập số của bạn: ")
            
            val userInput = readLine()
            val guess = userInput?.toIntOrNull()
            
            if (guess == null) {
                println("❌ Vui lòng nhập một số hợp lệ!")
                continue
            }
            
            attempts++
            
            when {
                guess == targetNumber -> {
                    println("🎉 Chúc mừng! Bạn đã đoán đúng số $targetNumber!")
                    println("🏆 Bạn đã thắng sau $attempts lần đoán!")
                    return
                }
                guess < targetNumber -> {
                    println("📈 Số bạn đoán nhỏ hơn! Thử số lớn hơn.")
                }
                else -> {
                    println("📉 Số bạn đoán lớn hơn! Thử số nhỏ hơn.")
                }
            }